[{"scene_id": "1948773244593127424", "uid": "985183999", "home_id": "111001032975", "name": "休眠", "template_id": "0", "type": 0, "local_dev": "", "enable": true, "enable_push": false, "common_use": false, "timespan": null, "scene_trigger": {"express": 1, "triggers": [{"id": 1, "order": 1, "src": "user", "key": "user.click", "extra": "", "name": "", "value": "", "value_type": 5, "extra_json": {"voice_alias": {"alias": [], "use_title": 1}}, "value_json": null, "protocol_type": 2, "sc_id": 1, "from": 0, "value_operation": 0, "std_sc_id": "", "duration": 0}]}, "scene_condition": {"express": 0, "conditions": []}, "scene_action": {"mode": 1, "actions": [{"group_id": 0, "id": 1, "order": 1, "type": 0, "name": "休眠", "payload": "", "payload_json": {"command": "set_power", "delay_time": 0, "device_name": "摄像机", "did": "369336401", "model": "chuangmi.camera.ip029a", "uid": "985183999", "value": "off"}, "protocol_type": 1, "sa_id": 5892, "from": 1, "device_group_id": 0, "nested_scene_info": null, "std_sa_id": ""}]}, "value_format": 1, "timewindow": {"from": "0 0 0 * * * *", "to": "0 0 0 * * * *", "filter": "", "extra": "", "time_zone": "Asia/Shanghai"}, "create_time": "1753458722", "update_time": "1753458789", "sub_usIds": [], "no_record_log": false, "parent_usId": "0", "scene_id_v1": "0", "room_id": "0", "common_used_roomIds": [], "tags": {}, "extra": null, "icon_url": "https://cnbj1.fds.api.xiaomi.com/scenetemplate/user_scene/a_batch.png", "enable_consist": false, "scene_version": 0, "temporary": false, "data_type": 0, "hm_extra_info": null}]