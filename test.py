import json
import time
import sys
sys.path.extend(['.', '..'])
from mijiaAPI import mijiaAPI

with open('jsons/auth.json', 'r', encoding='utf-8') as f:
    auth = json.load(f)
api = mijiaAPI(auth)

# ---------------------- get devices list ----------------------
devices = api.get_devices_list()
with open('jsons/devices.json', 'w', encoding='utf-8') as f:
    json.dump(devices, f, indent=2, ensure_ascii=False)
time.sleep(2)

# ---------------------- get homes list ------------------------
homes = api.get_homes_list()
with open('jsons/homes.json', 'w', encoding='utf-8') as f:
    json.dump(homes, f, indent=2, ensure_ascii=False)
time.sleep(2)

# ---------------------- get scenes list -----------------------
home_id = '111001032975'
scenes = api.get_scenes_list(home_id)
with open('jsons/scenes.json', 'w', encoding='utf-8') as f:
    json.dump(scenes, f, indent=2, ensure_ascii=False)
time.sleep(2)

# ---------------------- run scene -----------------------------
scence_id = scenes[0]['scene_id']
scence_name = scenes[0]['name']
ret = api.run_scene(scence_id)
print(f'运行场景 {scence_name}: {ret}')
time.sleep(2)
